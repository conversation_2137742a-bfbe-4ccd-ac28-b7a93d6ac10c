{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "noEmit": true, "declaration": true, "declarationMap": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "lib": ["ES2022", "DOM"], "types": ["bun-types", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}