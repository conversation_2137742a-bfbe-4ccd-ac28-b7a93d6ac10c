{"name": "vite-plugin-importmap", "version": "1.0.0", "description": "A Vite plugin that downloads external packages locally and generates importmap", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsdown", "dev": "tsdown --watch", "test": "bun test", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "keywords": ["vite", "plugin", "importmap", "esm", "modules"], "author": "", "license": "MIT", "devDependencies": {}, "dependencies": {"@types/cheerio": "^1.0.0", "@types/urijs": "^1.19.25", "cheerio": "^1.1.2", "es-module-lexer": "^1.7.0", "node-fetch": "^3.3.2", "semver": "^7.5.4", "urijs": "^1.19.11"}, "peerDependencies": {"vite": "^4.0.0 || ^5.0.0 || ^7.0.0"}}