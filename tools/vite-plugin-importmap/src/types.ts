export interface PluginOptions {
  /**
   * 下载包的本地目录
   * @default 'vendor'
   */
  publicDir?: string

  /**
   * url模板
   */
  urlTemplate?: string

  templates?: Record<string, string>

  /**
   * verbose
   */
  verbose?: boolean
}

// export interface PackageInfo {
//   name: string
//   version: string
//   main?: string
//   module?: string
//   exports?: Record<string, any>
//   dependencies?: Record<string, string>
//   peerDependencies?: Record<string, string>
// }

export interface DownloadResult {
  packageName: string
  version: string
  localPath: string
  entryPoint: string
  dependencies: string[]
}

export interface ImportMap {
  imports: Record<string, string>
  scopes?: Record<string, Record<string, string>>
}

export interface PackageMetadata {
  name: string
  version: string
  dist: {
    tarball: string
  }
  dependencies?: Record<string, string>
  peerDependencies?: Record<string, string>
}

export type PackageModule = {
  path: string
  name: string
  entry: string
  subpath?: string
}

export type PackageInfo = {
  name: string
  version: string
  module?: PackageModule

  template?: string
  localPath?: string
}

export type DependencyInfo = {
  url: string
  localPath: string
  filename: string
}