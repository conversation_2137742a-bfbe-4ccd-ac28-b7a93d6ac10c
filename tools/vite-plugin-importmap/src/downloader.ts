import { init, parse } from 'es-module-lexer'
import { existsSync, promises as fs } from 'fs'
import path, { dirname, join, resolve } from 'path'
import URI from "urijs"
import type { DependencyInfo, PackageInfo, PackageModule, PluginOptions } from './types'

export class PackageDownloader {
  private options: Required<PluginOptions>

  private _externals: Set<string> = new Set()

  downloadMap: Map<string, DependencyInfo> = new Map()
  localCopyMap: Set<string> = new Set()

  importMap: {
    imports: Record<string, string>;
  }

  constructor(options: PluginOptions = {}) {
    this.options = {
      publicDir: options.publicDir || 'vendor',
      urlTemplate: options.urlTemplate || 'https://esm.sh/{package}@{version}/{subpath}',
      templates: options.templates || {},
      verbose: options.verbose || false,
    }

    this.importMap = {
      imports: {
      }
    }
  }

  async setOptions(options: Partial<PluginOptions>) {
    this.options = {
      ...this.options,
      ...options
    }

    await fs.mkdir(this.options.publicDir, { recursive: true })
  }  

  getModulePath(path: string, name: string, subpath: string, packageJSON: any): PackageModule | undefined {
    if (!subpath.startsWith('.') && subpath !== '.') {
      subpath = `./${subpath}`
    }

    if (packageJSON.exports && packageJSON.exports[subpath]?.import) {
      let entry = packageJSON.exports[subpath].import
      if (typeof entry === 'object') {
        entry = entry.default
      }

      return {
        name,
        path,
        subpath,
        entry
      }
    }

    if (subpath == '.' && packageJSON.module) {
      return {
        name,
        path,
        entry: packageJSON.module
      }
    }

    return undefined
  }


  resolvePackageInfo(external: string): PackageInfo {
    // find node_modules/package.json from current dir to '/'
    let currentDir = process.cwd()
    while (true) {
      const packageJsonFolderPath = join(currentDir, 'node_modules', external)
      const packageJsonPath = join(packageJsonFolderPath, 'package.json')
      if (existsSync(packageJsonPath)) {
        const packageJson = require(packageJsonPath)
        return {
          name: external,
          version: packageJson.version,
          module: this.getModulePath(
            join(currentDir, 'node_modules'),
            external,
            '.',
            packageJson
            // '.', packageJsonFolderPath, packageJson
          ),
        }
      } else if (external.includes('/')) {
        let parts = external.split('/')
        let root = parts[0]
        let subpath = parts.slice(1).join('/')
        if (parts[0].startsWith('@')) {
          root = join(parts[0], parts[1])
          subpath = parts.slice(2).join('/')
        }
        const packageJsonFolderPath = join(currentDir, 'node_modules', root)
        const packageJsonPath = join(packageJsonFolderPath, 'package.json')

        if (existsSync(packageJsonPath)) {
          const packageJson = require(packageJsonPath)
          return {
            name: external,
            version: packageJson.version,
            module: this.getModulePath(
              join(currentDir, 'node_modules'),
              root, 
              subpath, 
              packageJson
            ),
          }
        }
      }

      const parentDir = dirname(currentDir)
      if (parentDir === currentDir) {
        break
      }
      currentDir = parentDir
    }

    throw new Error(`Cannot find package.json for ${external}`)
  }


  resolvePackageInfos(externals: Set<string>) {
    const packageInfos: PackageInfo[] = []
    for (const name of externals) {
      const info = this.resolvePackageInfo(name)
      packageInfos.push(info)
    }
    return packageInfos
  }

  async resolvePackages(packageInfos: PackageInfo[]): Promise<void> {
    const externals = packageInfos.map(i => i.name)
    await Promise.all(packageInfos.map(async i => this.resolvePackage(i)))
  }

  private async resolvePackage(packageInfo: PackageInfo): Promise<void> {
    this._externals.add(packageInfo.name)
    return packageInfo.module ?
      this.copyPackage(packageInfo) :
      this.downloadPackage(packageInfo)
  }

  private async ensureFileSave(filePath: string) {
    await fs.mkdir(dirname(filePath), { recursive: true })
  }

  private async copyPackage(packageInfo: PackageInfo): Promise<void> {
    const basePath = join(this.options.publicDir, packageInfo.module!.name)
    const destFile = join(basePath, packageInfo.module!.entry)

    const originalFile = join(packageInfo.module!.path, packageInfo.module!.name, packageInfo.module!.entry)
    const content = (await fs.readFile(originalFile)).toString()
    await this.ensureFileSave(destFile)

    await fs.writeFile(destFile, content.replaceAll("process.env.NODE_ENV", `"${process.env.NODE_ENV}"`))
    // await fs.writeFile(destFile, content)

    await this.resolveLocalDependencies(content, dirname(originalFile), dirname(destFile))

    this.localCopyMap.add(destFile)

    const external = packageInfo.module!.subpath ? join(packageInfo.module!.name, packageInfo.module!.subpath) : packageInfo.module!.name

    const relativePath = join(packageInfo.module!.name, packageInfo.module!.entry)
    this.importMap.imports[external] = `/${relativePath}`
  }

  private async resolveLocalDependencies(code: string, originalDir: string, destDir: string): Promise<void> {
    await init;
    const [imports, exports] = parse(code);
    let paths = imports.map(i => {
      const full = code.substring(i.s - 1, i.e + 1)

      return {
        path: code.substring(i.s, i.e),
        isStatic: (full.startsWith("'") && full.endsWith("'")) ||
          (full.startsWith('"') && full.endsWith('"'))
      }
    });

    for (let p of paths) {
      const { path, isStatic } = p

      if (!isStatic) {
        continue
      }

      if (this._externals.has(path)) {
        continue
      }
      
      if (this.localCopyMap.has(path)) {
        continue
      }

      // absolute -> external
      if (!path.startsWith('.')) {
        const packageInfo = this.resolvePackageInfo(path)
        await this.resolvePackage(packageInfo)
        return
      }

      const originalFile = resolve(originalDir, path)
      const destFile = resolve(destDir, path)
      if (!existsSync(originalFile)) {
        throw new Error(`${originalFile} not found deps of ${destDir}`)
      }

      const content = (await fs.readFile(originalFile)).toString()

      await this.ensureFileSave(destFile)
      await fs.writeFile(destFile, content.replaceAll("process.env.NODE_ENV", `"${process.env.NODE_ENV}"`))
      // await fs.writeFile(destFile, content)

      await this.resolveLocalDependencies(content, dirname(originalFile), dirname(destFile))
      this.localCopyMap.add(destFile)
    }

  }

  private async downloadPackage(packageInfo: PackageInfo): Promise<void> {
    this.log(`📦 Downloading package: ${packageInfo.name}`)

    let template = packageInfo.template || this.options.urlTemplate
    let parts = packageInfo.name.split('/')
    let subpath = parts[1]

    let url = template
      .replace('{package}', subpath ? parts[0] : packageInfo.name)
      .replace('{version}', packageInfo.version)
      .replace('{subpath}', subpath || '')

    if (url.endsWith('/')) {
      url = url.slice(0, -1)
    }

    this.log(`downloading package ${packageInfo.name} from ${url}`)

    try {
      const filename = `_${packageInfo.name.replace(/\//g, '_')}.js`
      const filePath = join(this.options.publicDir, filename)
      let content = await this.getJsContent(url)
      packageInfo.localPath = filePath
      this.importMap.imports[packageInfo.name] = `/${filename}`

      await this.resolveDependencies(content, {
        localPath: this.options.publicDir,
        filename,
        url
      })
    } catch (err) {
      console.error(`❌ Failed to download package ${packageInfo.name}:`, err)
      throw err
    }

  }

  private async resolveDependencies(code: string, dependencyInfo: DependencyInfo): Promise<void> {
    this.downloadMap.set(dependencyInfo.url, dependencyInfo)

    await init;
    const [imports, exports] = parse(code);
    let paths = imports.map(i => code.substring(i.s, i.e));

    let rewrites: Record<string, string> = {}

    const dependencies: DependencyInfo[] = paths.map( p => {
      if (p.startsWith('http')) {
        const parts = p.split('/')
        // unsafe
        parts.unshift()
        parts.pop()

        let filename = URI(p).filename()
        if (!filename.endsWith('.js') && !filename.endsWith('.mjs')) {
          rewrites[p] = p.replace(filename, `${filename}.js`)
          filename = `${filename}.js`
        }

        return {
          url: p,
          localPath: path.join(this.options.publicDir, parts.join('/')),
          filename,
        }
      } else {
        const url = new URL(p, dependencyInfo.url)
        const parts = p.split('/')
        // unsafe
        parts.unshift()
        parts.pop()

        let filename = URI(url.href).filename()
        if (!filename.endsWith('.js') && !filename.endsWith('.mjs')) {
          rewrites[p] = p.replace(filename, `${filename}.js`)
          filename = `${filename}.js`
        }

        return {
          url: url.href,
          localPath: path.join(this.options.publicDir, parts.join('/')),
          filename,
        }
      }
    })

    this.log(`finding dependencies for ${dependencyInfo.url}`)
    this.log(dependencies)

    for (const dependency of dependencies) {
      if (this.downloadMap.has(dependency.url)) {
        continue
      }

      this.log(`downloading ${dependency.url}: by ${dependencyInfo.filename}`)
      const content = await this.getJsContent(dependency.url)
      await this.resolveDependencies(content, dependency)
    }

    await fs.mkdir(dependencyInfo.localPath, { recursive: true })
    const filePath = join(dependencyInfo.localPath, dependencyInfo.filename)
    const rewriteCode = await this.rewriteImports(code, i => rewrites[i] || i)
    await fs.writeFile(filePath, rewriteCode)

  }

  private async getJsContent(url: string) {
    console.log('fetching....')
    const result = await fetch(url)
    console.log('fetch result', result)
    let content = ''
    if (result.ok) {
      content = await result.text()
    }
    return content
  }

  private async rewriteImports(code: string, callback: (importPath: string) => string) {
    await init;
    const [imports, exports] = parse(code);
    let rewritten = '';
    let lastIndex = 0;
    for (const imp of imports) {
      // imp.s, imp.e are start/end of import path string
      const importPath = code.substring(imp.s, imp.e);
      let newPath = callback(importPath);
      // Append code before import path
      rewritten += code.substring(lastIndex, imp.s);
      // Append rewritten import path
      rewritten += newPath;
      lastIndex = imp.e;
    }
    // Append remaining code after last import
    rewritten += code.substring(lastIndex);
    return rewritten;
  }

  private log(message: any): void {
    if (this.options.verbose) {
      console.log(message)
    }
  }
}
