import type { OutputBundle } from 'rollup'
import type { Plugin } from 'vite'
import { PackageDownloader } from './downloader'
import type { PluginOptions } from './types'

export function importmapDownloader(options: PluginOptions = {}): Plugin {
  const downloader = new PackageDownloader(options)
  // 辅助函数：获取外部依赖
  function getExternalDependencies(bundle: OutputBundle): Set<string> {
    const externals = new Set<string>()

    for (const [, chunk] of Object.entries(bundle)) {
      if (chunk.type === 'chunk') {

        // 收集所有外部导入
        if (chunk.imports) {
          chunk.imports.forEach((imp: string) => {
            if (!imp.endsWith('.js')) {
              externals.add(imp)
            }
          })
        }

        if (chunk.dynamicImports) {
          console.log("dynamicImports: ", chunk.dynamicImports)
        }


        // 收集动态导入
        // if (chunk.dynamicImports) {
        //   chunk.dynamicImports.forEach((imp: string) => {
        //     if (!imp.startsWith('.') && !imp.startsWith('/')) {
        //       const packageName = imp.startsWith('@')
        //         ? imp.split('/').slice(0, 2).join('/')
        //         : imp.split('/')[0]
        //       externals.add(packageName)
        //     }
        //   })
        // }
      }
    }

    return externals
  }


  return {
    name: 'vite-plugin-importmap',
    transformIndexHtml(html, ctx) {
      return html;
      // Use cheerio to parse and manipulate HTML
      // if (!(process.env.NODE_ENV === 'production' || typeof window === 'undefined')) {
      //   return html
      // }

      const cheerio = require('cheerio')
      const $ = cheerio.load(html)

      // Create the importmap script element
      const scriptContent = JSON.stringify(downloader.importMap, null, 2)
      const scriptTag = `<script type="importmap">${scriptContent}</script>`

      // Insert before all other script elements in head
      const head = $('head')
      if (head.length > 0) {
        // Find first script in head
        const firstScript = head.find('script').first()
        if (firstScript.length > 0) {
          firstScript.before(scriptTag)
        } else {
          head.append(scriptTag)
        }
      } else {
        // If no head, prepend to html
        $.root().prepend(scriptTag)
      }

      return $.html()
    },

    async generateBundle(outputOptions, bundle) {
      try {
        // 分析所有的外部依赖
        const externalDeps = getExternalDependencies(bundle)

        if (externalDeps.size === 0) {
          console.log('📦 No external dependencies found')
          return
        }

        console.log('process.env', process.env.NODE_ENV)
        console.log(`📦 Found ${externalDeps.size} external dependencies:`, Array.from(externalDeps))

        await downloader.setOptions({
          publicDir: outputOptions.dir!,
        })

        const packagesInfos = downloader.resolvePackageInfos(externalDeps)
        // 下载所有外部依赖到本地
        await downloader.resolvePackages(packagesInfos)

        console.log(downloader.importMap)

        // // 生成 importmap
        // const importMap = await importMapGenerator.generate(downloadResults)

        // // 将 importmap 添加到输出包中
        // this.emitFile({
        //   type: 'asset',
        //   fileName: options.importMapFileName || 'importmap.json',
        //   source: JSON.stringify(importMap, null, 2)
        // })

        console.log('✅ Importmap generated successfully!')
      } catch (error) {
        console.error('❌ Error in importmap downloader plugin:', error)
        throw error
      }

    }
  }
}

export default importmapDownloader
export type { PluginOptions } from './types'
