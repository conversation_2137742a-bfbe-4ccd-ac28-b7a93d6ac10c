import MagicString from "magic-string";
import { createFilter, FilterPattern } from "@rollup/pluginutils";
import type { PluginContext, TransformResult } from "rollup";
import type { Plugin } from "vite";

import importToGlobals from "./lib/import-to-globals";
import { copyPackage, resolvePackageInfo } from "./lib/copy-externals";
import { join } from "path";
import cheerio from "cheerio";

export type VariableName = string;

/**
 * globals is a moduleId/variableName map
 * or provide a function that takes the moduleId and returns the variableName
 */
export type ModuleNameMap = Record<string, string>

export interface ExternalGlobalsOptions {
  /**
   * [include] is a valid `picomatch` glob pattern, or array of patterns. If defined, only matched files would be transformed.
   */
  include?: FilterPattern;
  /**
   * [exclude] is a valid `picomatch` glob pattern, or array of patterns. Matched files would not be transformed.
   */
  exclude?: FilterPattern;
  /**
   * [dynamicWrapper] is used to specify dynamic imports. It accepts a variable name and returns an expression
   */
  dynamicWrapper?: (variableName: VariableName) => string;

  /**
   * [constBindings] is used to decide whether to use `const` to declare variables. Default is `false`
   */
  constBindings?: boolean;
}

const defaultDynamicWrapper = (id: string): string => `Promise.resolve(${id})`;

function isVirtualModule(id: string): boolean {
  return id.startsWith("\0");
}

function createPlugin(
  globals: ModuleNameMap,
  pluginOptions: ExternalGlobalsOptions = {}
): Plugin {
  const { include, exclude, dynamicWrapper = defaultDynamicWrapper, constBindings = false } = pluginOptions;

  let srcs: string[] = []

  if (!globals) {
    throw new TypeError("Missing mandatory option 'globals'");
  }

  let getName: (name: string) => string | undefined = () => undefined;
  const globalsType = typeof globals;
  const isGlobalsObj = globalsType === "object";

  if (isGlobalsObj) {
    const globalsObj = globals as Record<string, string>;
    getName = function (name: string): string | undefined {
      if (Object.prototype.hasOwnProperty.call(globalsObj, name)) {
        return globalsObj[name];
      }
      return undefined;
    };
  } else if (globalsType !== "function") {
    throw new TypeError(`Unexpected type of 'globals', got '${globalsType}'`);
  }

  const dynamicWrapperType = typeof dynamicWrapper;
  if (dynamicWrapperType !== "function") {
    throw new TypeError(`Unexpected type of 'dynamicWrapper', got '${dynamicWrapperType}'`);
  }

  async function resolveId(
    importee: string,
    _: string | undefined,
    options: { isEntry?: boolean }
  ): Promise<boolean | null> {
    if (isVirtualModule(importee) || options.isEntry) return null;
    const globalName = getName(importee);
    return globalName ? false : null;
  }

  const filter = createFilter(include, exclude);

  function getDebug(context: PluginContext) {
    return (err: Error & { loc?: any; pos?: any }, message: string) => {
      if ('debug' in context && typeof context.debug === 'function') {
        context.debug({
          message,
          cause: err
        });
      } else if (context.warn) {
        context.warn(message);
      }
    };
  }

  async function buildStart(rawOptions: any): Promise<any> {
    const plugins = Array.isArray(rawOptions.plugins)
      ? [...rawOptions.plugins]
      : rawOptions.plugins
        ? [rawOptions.plugins]
        : [];
    plugins.unshift({
      name: 'vite-plugin-external-globals--resolver',
      resolveId
    });
    return { ...rawOptions, plugins };
  }

  async function transform(
    this: PluginContext,
    code: string,
    id: string
  ): Promise<TransformResult> {
    if (
      (!isVirtualModule(id) && !filter(id)) ||
      (isGlobalsObj && Object.keys(globals as Record<string, string>).every(moduleId => !code.includes(moduleId)))
    ) {
      return null;
    }

    let ast: any;
    try {
      ast = this.parse(code);
    } catch (err) {
      getDebug(this)(err as Error, `Failed to parse code, skip ${id}`);
      return null;
    }

    const magicString = new MagicString(code);
    const isTouched = await importToGlobals({
      ast,
      code: magicString,
      getName,
      getDynamicWrapper: dynamicWrapper,
      constBindings
    });

    return isTouched ? {
      code: magicString.toString(),
      map: magicString.generateMap()
    } : null;
  }

  return {
    name: "vite-plugin-external-globals",
    buildStart,
    transform,
    async generateBundle(outputOptions, bundle) {
      const externals = Object.keys(globals)
      const vendorDir = join(outputOptions.dir || ".", "vendor");
      const packageInfos = externals.map(external => resolvePackageInfo(external))

      await Promise.all(packageInfos.map(p => copyPackage(vendorDir, p)))
      srcs = packageInfos.map(p => {
        return '/' + join('vendor', p.module!.name, p.module!.entry)
      })
      console.log('srcs: ', srcs);

    },
    transformIndexHtml(html, ctx) {
      const $ = cheerio.load(html)

      // Create the importmap script element
      const scriptTags = srcs.map(src => `<script src="${src}"></script>`)

      // Insert before all other script elements in head
      const head = $('head')
      if (head.length > 0) {
        // Find first script in head
        const firstScript = head.find('script').first()
        if (firstScript.length > 0) {
          firstScript.before(scriptTag)
        } else {
          head.append(scriptTag)
        }
      } else {
        // If no head, prepend to html
        $.root().prepend(scriptTag)
      }

      return $.html()
    }


  };
}

export default createPlugin;


