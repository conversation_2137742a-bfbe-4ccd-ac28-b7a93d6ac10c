import { attachScopes, makeLegalIdentifier } from "@rollup/pluginutils";
import type Magic<PERSON>tring from "magic-string";

let walk: any, isReference: any;

async function prepare(): Promise<void> {
  [{ walk }, { default: isReference }] = await Promise.all([
    import("estree-walker"),
    import("is-reference")
  ]);
}

interface ImportSpec {
  local: { name: string; start: number; end: number };
  imported?: { name: string };
  exported?: { name: string; start: number; end: number };
  isOverwritten?: boolean;
  start: number;
  end: number;
}

interface ASTNode {
  type: string;
  start: number;
  end: number;
  source?: { value: string; start: number; end: number };
  specifiers?: ImportSpec[];
  declaration?: any;
  body?: ASTNode[];
  name?: string;
  scope?: any;
  arguments?: { value: string }[];
  callee?: { type: string };
  key?: { start: number; end: number; isOverwritten?: boolean };
  value?: { start: number; end: number; isOverwritten?: boolean };
}

function analyzeImport(
  node: ASTNode,
  importBindings: Map<string, string>,
  code: MagicString,
  getName: (name: string) => string | undefined,
  globals: Set<string>
): boolean {
  const name = node.source?.value && getName(node.source.value);
  if (!name) {
    return false;
  }
  globals.add(name);
  if (node.specifiers) {
    for (const spec of node.specifiers) {
      importBindings.set(spec.local.name, makeGlobalName(
        spec.imported ? spec.imported.name : "default",
        name
      ));
    }
  }
  code.remove(node.start, node.end);
  return true;
}

function makeGlobalName(prop: string, name: string): string {
  if (prop === "default") {
    return name;
  }
  return `${name}.${prop}`;
}

function writeSpecLocal(
  code: MagicString,
  root: ASTNode,
  spec: ImportSpec,
  name: string,
  tempNames: Set<string>,
  constBindings: boolean
): void {
  if (spec.isOverwritten) return;
  // we always need an extra assignment for named export statement
  // https://github.com/eight04/rollup-plugin-external-globals/issues/19
  const localName = `_global_${makeLegalIdentifier(name)}`;
  if (!tempNames.has(localName)) {
    code.appendRight(root.start, `${constBindings ? "const" : "var"} ${localName} = ${name};\n`);
    tempNames.add(localName);
  }
  if (spec.local.name === localName) {
    return;
  }
  if (spec.exported && spec.local.start === spec.exported.start && spec.local.end === spec.exported.end) {
    code.appendRight(spec.local.start, `${localName} as `);
  } else {
    code.overwrite(spec.local.start, spec.local.end, localName);
  }
  spec.isOverwritten = true;
}

function writeIdentifier(code: MagicString, node: ASTNode, parent: ASTNode, name: string): void {
  if (node.name === name || (node as any).isOverwritten) {
    return;
  }
  // 2020/8/14, parent.key and parent.value is no longer the same object. However, the shape is the same.
  if (parent.type === "Property" && parent.key && parent.value && parent.key.start === parent.value.start) {
    code.appendLeft(node.end, `: ${name}`);
    parent.key.isOverwritten = true;
    parent.value.isOverwritten = true;
  } else if (parent.type === "ExportSpecifier") {
    const parentSpec = parent as any;
    if (parentSpec.local && parentSpec.exported && parentSpec.local.start === parentSpec.exported.start) {
      code.appendLeft(node.start, `${name} as `);
      parentSpec.local.isOverwritten = true;
      parentSpec.exported.isOverwritten = true;
    }
  } else {
    code.overwrite(node.start, node.end, name, { contentOnly: true });
    // FIXME: do we need this?
    (node as any).isOverwritten = true;
  }
}

function analyzeExportNamed(
  node: ASTNode,
  code: MagicString,
  getName: (name: string) => string | undefined,
  tempNames: Set<string>,
  constBindings: boolean
): boolean {
  if (node.declaration || !node.source || !node.source.value) {
    return false;
  }
  const name = getName(node.source.value);
  if (!name) {
    return false;
  }
  if (node.specifiers) {
    for (const spec of node.specifiers) {
      const globalName = makeGlobalName(spec.local.name, name);
      writeSpecLocal(code, node, spec, globalName, tempNames, constBindings);
    }
    if (node.specifiers.length) {
      code.overwrite(node.specifiers[node.specifiers.length - 1].end, node.source.end, "}");
    } else {
      code.remove(node.start, node.end);
    }
  }
  return true;
}

function analyzeExportAll(
  node: ASTNode,
  code: MagicString,
  getName: (name: string) => string | undefined
): void {
  const name = node.source?.value && getName(node.source.value);
  if (!name) {
    return;
  }
  throw new Error("Cannot export all properties from an external variable");
}

function writeDynamicImport(code: MagicString, node: ASTNode, content: string): void {
  code.overwrite(node.start, node.end, content);
}

function getDynamicImportSource(node: ASTNode): string | undefined {
  if (node.type === "ImportExpression") {
    return node.source?.value;
  }
  if (node.type === "CallExpression" && node.callee?.type === "Import" && node.arguments?.[0]) {
    return node.arguments[0].value;
  }
  return undefined;
}

// export left hand analyzer
class ExportLeftHand {
  inDeclaration = false;
  inLeftHand = false;

  enter(node: ASTNode, parent?: ASTNode): void {
    if (parent && parent.type === "Program") {
      this.inDeclaration = node.type === "ExportNamedDeclaration";
    }
    if (this.inDeclaration && parent?.type === "VariableDeclarator" && (parent as any).id === node) {
      this.inLeftHand = true;
    }
  }

  leave(node: ASTNode, parent?: ASTNode): void {
    if (this.inLeftHand && parent?.type === "VariableDeclarator") {
      this.inLeftHand = false;
    }
  }
}

interface ImportToGlobalsOptions {
  ast: ASTNode;
  code: MagicString;
  getName: (name: string) => string | undefined;
  getDynamicWrapper: (name: string) => string;
  constBindings: boolean;
}

async function importToGlobals({
  ast,
  code,
  getName,
  getDynamicWrapper,
  constBindings
}: ImportToGlobalsOptions): Promise<boolean> {
  await prepare();
  let scope = attachScopes(ast, "scope");
  const bindings = new Map<string, string>();
  const globals = new Set<string>();
  let isTouched = false;
  const tempNames = new Set<string>();
  const exportLeftHand = new ExportLeftHand();

  if (ast.body) {
    for (const node of ast.body) {
      if (node.type === "ImportDeclaration") {
        isTouched = analyzeImport(node, bindings, code, getName, globals) || isTouched;
      } else if (node.type === "ExportNamedDeclaration") {
        isTouched = analyzeExportNamed(node, code, getName, tempNames, constBindings) || isTouched;
      } else if (node.type === "ExportAllDeclaration") {
        analyzeExportAll(node, code, getName);
      }
    }
  }

  let topStatement: ASTNode | undefined;
  walk(ast, {
    enter(node: ASTNode, parent?: ASTNode) {
      exportLeftHand.enter(node, parent);
      if (parent && parent.type === "Program") {
        topStatement = node;
      }
      if (/^importdec/i.test(node.type)) {
        (this as any).skip();
        return;
      }
      if (node.scope) {
        scope = node.scope;
      }
      if (isReference(node, parent)) {
        if (node.name && bindings.has(node.name) && !scope.contains(node.name)) {
          if (parent?.type === "ExportSpecifier") {
            if (topStatement) {
              writeSpecLocal(code, topStatement, parent as any, bindings.get(node.name)!, tempNames, constBindings);
            }
          } else {
            writeIdentifier(code, node, parent!, bindings.get(node.name)!);
          }
        } else if (node.name && globals.has(node.name) && scope.contains(node.name)) {
          // conflict with local variable
          writeIdentifier(code, node, parent!, `_local_${node.name}`);
          if (exportLeftHand.inLeftHand && topStatement) {
            code.appendLeft(topStatement.end, `export {_local_${node.name} as ${node.name}};\n`);
            if (topStatement.declaration) {
              code.remove(topStatement.start, topStatement.declaration.start);
            }
          }
        }
      }
      const source = getDynamicImportSource(node);
      const name = source && getName(source);
      const dynamicName = name && getDynamicWrapper(name);
      if (dynamicName) {
        writeDynamicImport(code, node, dynamicName);
        isTouched = true;
        (this as any).skip();
      }
    },
    leave(node: ASTNode, parent?: ASTNode) {
      exportLeftHand.leave(node, parent);
      if (node.scope) {
        scope = node.scope.parent;
      }
    }
  });

  return isTouched;
}

export default importToGlobals;
