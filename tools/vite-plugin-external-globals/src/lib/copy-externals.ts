import { existsSync, promises as fs } from "fs"
import { dirname, join } from "path"

export type PackageModule = {
  path: string
  name: string
  entry: string
  subpath?: string
}

export type PackageInfo = {
  name: string
  version: string
  module?: PackageModule

  template?: string
  localPath?: string
}
function getModulePath(path: string, name: string, subpath: string, packageJSON: any): PackageModule | undefined {
  if (!subpath.startsWith('.') && subpath !== '.') {
    subpath = `./${subpath}`
  }

  if (packageJSON.exports && packageJSON.exports[subpath]?.umd) {
    let entry = packageJSON.exports[subpath].umd
    if (typeof entry === 'object') {
      entry = entry.default
    }

    return {
      name,
      path,
      subpath,
      entry
    }
  }

  if (subpath == '.' && packageJSON.unpkg) {
    return {
      name,
      path,
      entry: packageJSON.unpkg
    }
  }

  return undefined
}


export function resolvePackageInfo(external: string): PackageInfo {
  // find node_modules/package.json from current dir to '/'
  let currentDir = process.cwd()
  while (true) {
    const packageJsonFolderPath = join(currentDir, 'node_modules', external)
    const packageJsonPath = join(packageJsonFolderPath, 'package.json')
    if (existsSync(packageJsonPath)) {
      const packageJson = require(packageJsonPath)
      return {
        name: external,
        version: packageJson.version,
        module: getModulePath(
          join(currentDir, 'node_modules'),
          external,
          '.',
          packageJson
          // '.', packageJsonFolderPath, packageJson
        ),
      }
    } else if (external.includes('/')) {
      let parts = external.split('/')
      let root = parts[0]
      let subpath = parts.slice(1).join('/')
      if (parts[0].startsWith('@')) {
        root = join(parts[0], parts[1])
        subpath = parts.slice(2).join('/')
      }
      const packageJsonFolderPath = join(currentDir, 'node_modules', root)
      const packageJsonPath = join(packageJsonFolderPath, 'package.json')

      if (existsSync(packageJsonPath)) {
        const packageJson = require(packageJsonPath)
        return {
          name: external,
          version: packageJson.version,
          module: getModulePath(
            join(currentDir, 'node_modules'),
            root,
            subpath,
            packageJson
          ),
        }
      }
    }

    const parentDir = dirname(currentDir)
    if (parentDir === currentDir) {
      break
    }
    currentDir = parentDir
  }

  throw new Error(`Cannot find package.json for ${external}`)
}

async function ensureFileSave(filePath: string) {
  await fs.mkdir(dirname(filePath), { recursive: true })
}

export async function copyPackage(distDir: string, packageInfo: PackageInfo): Promise<void> {
  const destFile = join(distDir, packageInfo.module!.name, packageInfo.module!.entry)

  const originalFile = join(packageInfo.module!.path, packageInfo.module!.name, packageInfo.module!.entry)
  const content = (await fs.readFile(originalFile)).toString()
  await ensureFileSave(destFile)

  await fs.writeFile(destFile, content)

  // const external = packageInfo.module!.subpath ? join(packageInfo.module!.name, packageInfo.module!.subpath) : packageInfo.module!.name
}