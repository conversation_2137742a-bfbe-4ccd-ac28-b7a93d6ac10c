{"name": "vite-plugin-external-globals", "version": "0.13.0", "description": "Transform external imports into global variables like output.globals.", "keywords": ["vite-plugin", "es", "transform", "external", "globals"], "module": "dist/index.mjs", "typings": "dist/index.d.mts", "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "files": ["dist"], "eslintIgnore": ["coverage"], "scripts": {"build": "tsdown src/index.ts --watch --out-dir dist --format esm --dts", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build", "test": "npm run build && eslint . --cache && c8 --reporter lcov mocha", "preversion": "npm test", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/eight04/rollup-plugin-external-globals.git"}, "author": "eight04 <<EMAIL>>", "license": "MIT", "devDependencies": {"@eslint/js": "^9.9.0", "@rollup/plugin-commonjs": "^28.0.1", "@types/node": "^24.5.2", "c8": "^10.1.2", "endent": "^2.1.0", "eslint": "^9.5.0", "globals": "^15.9.0", "mocha": "^10.5.2", "rollup": "^4.18.0", "rollup2": "npm:rollup@^2.79.2", "tempdir-yaml": "^0.3.0", "tsdown": "^0.15.2", "typescript": "^5.9.2"}, "dependencies": {"@rollup/pluginutils": "^5.1.0", "estree-walker": "^3.0.3", "is-reference": "^3.0.2", "magic-string": "^0.30.10"}, "peerDependencies": {"rollup": "^2.25.0 || ^3.3.0 || ^4.1.4"}}