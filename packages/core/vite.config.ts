import { defineConfig } from "vite";
// import react from "@vitejs/plugin-react";
// import tailwindcss from '@tailwindcss/vite';
// import { importmapDownloader } from 'vite-plugin-importmap-downloader'
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";
import dts from 'vite-plugin-dts'

const host = process.env.TAURI_DEV_HOST;
const __dirname = dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    dts(),
    // importmapDownloader() as any,
    // react(), 
    // tailwindcss(), 
  ],
  build: {
    minify: false,
    lib: {
      entry: {
        'index': resolve(__dirname, './src/index.ts'),
        'tauri': resolve(__dirname, './src/tauri/index.ts'),
        'utils': resolve(__dirname, './src/utils/index.ts'),
        'api': resolve(__dirname, './src/api/index.ts'),
      },
      formats: ['es'],
    },
    rollupOptions: {
      external: [
        'react', 
        'react-dom/client',
      ],
    }
  },
  

  // CSS configuration for SCSS modules
  css: {
    modules: {
      // Enable CSS modules for files ending with .module.scss
      localsConvention: 'camelCase',
      generateScopedName: '[name]__[local]___[hash:base64:5]',
    },
    preprocessorOptions: {
      scss: {
        // Additional SCSS options can be added here
        silenceDeprecations: ["legacy-js-api"],
      },
    },
  },

  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent Vite from obscuring rust errors
  // clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1421,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
          protocol: "ws",
          host,
          port: 1421,
        }
      : undefined,
    watch: {
      // 3. tell Vite to ignore watching `src-tauri`
      // ignored: ["**/src-tauri/**"],
    },
  },
});
