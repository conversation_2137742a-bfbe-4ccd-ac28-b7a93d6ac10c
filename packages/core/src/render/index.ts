import type { JSX } from "react";
import { get<PERSON><PERSON><PERSON>, Manager } from "../base/manager";
import { PluginManager } from "../plugin";

export enum RenderEvent {
  Main,
  AddTask,
}

type RenderCallback<T> = (payload: T) => Promise<() => JSX.Element>

export class RenderManager extends Manager {
  private _callbacks: Map<RenderEvent, Map<string, RenderCallback<any>>> = new Map()

  hook<T, R>(event: RenderEvent, id: string, cb: RenderCallback<T>) {
    if (!this._callbacks.has(event)) {
      this._callbacks.set(event, new Map())
    }
    const callbacks = this._callbacks.get(event)
    if (callbacks?.has(id)) {
      console.warn(`Render callback ${id} already registered`)
    } else {
      callbacks?.set(id, cb)
    }
  }

  async trigger<T>(event: RenderEvent, id: string, payload: T): Promise<undefined | (() => JSX.Element)> {
    const callbacks = this._callbacks.get(event)
    return callbacks?.get(id)?.(payload)
  }
}
