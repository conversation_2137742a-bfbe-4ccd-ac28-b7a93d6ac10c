import { get<PERSON><PERSON><PERSON>, Manager } from "../base/manager";
import { PluginContext } from "./context";
import type { BasePlugin, IPlugin } from "./plugin";
import type { PluginManifest, PluginResolve } from "./types";

type OverridePluginFn = (manifest: PluginManifest) => Promise<any>

export class PluginManager extends Manager {
  private _pluginResolves: Map<string, PluginResolve> = new Map();
  private _plugins: Map<string, IPlugin> = new Map();
  private _dev_overridePlugin: OverridePluginFn | null = null
  constructor() {
    super();
  }

  setOverridePlugin(overridePluginFn: OverridePluginFn) {
    this._dev_overridePlugin = overridePluginFn
  }

  get dev_overridePlugin() {
    return this._dev_overridePlugin
  }

  async register(pluginManifest: PluginManifest): Promise<IPlugin | null> {
    const pluginImpl = new PluginResolveImpl(pluginManifest);
    this._pluginResolves.set(pluginManifest.id, pluginImpl);
    try {
      const plugin = await pluginImpl.resolve(!!this.dev_overridePlugin);
      plugin.onActivate()
      return plugin
    } catch (error) {
      console.error(`error in register: `, error)
    }
    return null
  }

  async registerAll(pluginManifests: PluginManifest[]): Promise<IPlugin[]> {
    const plugins = await Promise.all(pluginManifests.map(pluginManifest => this.register(pluginManifest)));
    return plugins.filter(p => p) as IPlugin[]
  }

  async resolve(id: string): Promise<IPlugin> {
    const plugin = this._plugins.get(id);
    if (!plugin) {
      const pluginResolve = this._pluginResolves.get(id);
      if (!pluginResolve) {
        throw new Error(`Plugin ${id} not found`);
      }
      const pluginInstance = await pluginResolve.resolve();
      this._plugins.set(id, pluginInstance);
      return pluginInstance;
    }
    return plugin;
  }

}

export class PluginResolveImpl implements PluginResolve {
  private _modulePath: string;

  constructor(private _manifest: PluginManifest) {
    const blob = new Blob([_manifest.js], { type: 'application/javascript' });
    this._modulePath = URL.createObjectURL(blob);
  }
  async resolve(dev: boolean): Promise<IPlugin> {
    let PluginCtor: typeof BasePlugin
    if (dev) {
      const plugin = getManager(PluginManager)
      PluginCtor = await plugin.dev_overridePlugin?.(this._manifest)
    } else {
      PluginCtor = await import(/* @vite-ignore */ this._modulePath).then(module => module.default);
    }
    return new PluginCtor(this._manifest, getManager(PluginContext));
  }

  get manifest(): PluginManifest {
    return this._manifest;
  }

  get modulePath(): string {
    return this._modulePath
  }
}