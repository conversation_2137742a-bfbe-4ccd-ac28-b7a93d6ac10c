import { type PluginManifest } from "./types";


export interface IPlugin {
  id: string;
  manifest: PluginManifest;
  context: IPluginContext;

  onActivate(): void;
  onDeactivate(): void;
  onInstall(): void;
  onUninstall(): void;
  onUpdate(): void;
}

export class BasePlugin implements IPlugin {
  constructor(
    protected _manifest: PluginManifest,
    protected _context: IPluginContext
  ) {
  }

  get id(): string {
    return this.manifest.id;
  }

  get manifest(): PluginManifest {
    return this._manifest;
  }

  get context(): IPluginContext {
    return this._context;
  }

  onActivate(): void {
    console.log("Base Plugin activated");
  }
  onDeactivate(): void {
    console.log("Base Plugin deactivated");
  }
  onInstall(): void {
    console.log("Base Plugin installed");
  }
  onUninstall(): void {
    console.log("Base Plugin uninstalled");
  }
  onUpdate(): void {
    console.log("Base Plugin updated");
  }
}

export interface IPluginContext {
  do() : void;
}