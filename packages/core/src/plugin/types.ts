import type { IPlugin } from "./plugin"

export interface PluginResolve {
  manifest: PluginManifest
  modulePath: string
  resolve(dev?: boolean): Promise<IPlugin>
}

export type PluginCategory = 'video' | 'audio' | 'image' |'text' | 'other'
export interface PluginManifest {
  id: string
  display_name: string
  version: string
  icon: string
  description: string
  category: PluginCategory
  js: string
  run_at: string
}

