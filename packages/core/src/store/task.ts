// stores/task.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const inEdit = ref(false)
  const inDetail = ref(false)
  const inFilter = ref(false)

  // Actions
  const setInEdit = (value: boolean) => {
    inEdit.value = value
  }

  const setInDetail = (value: boolean) => {
    inDetail.value = value
  }

  const setInFilter = (value: boolean) => {
    inFilter.value = value
  }

  // 返回所有状态和方法
  return {
    inEdit,
    inDetail,
    inFilter,
    setInEdit,
    setInDetail,
    setInFilter
  }
})