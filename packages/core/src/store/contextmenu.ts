import { defineStore } from 'pinia'
import { ref } from 'vue'
import { nanoid } from 'nanoid'

type ContextMenuState = {
  [key: string]: string
}

export const useContextMenuStore = defineStore('contextmenu', () => {
  // state
  const contextMenuState = ref<ContextMenuState>({})

  // actions
  const contextMenuChanged = (id?: string) => {
    const key = nanoid()
    if (id) {
      contextMenuState.value[id] = key
    } else {
      // change all
      Object.keys(contextMenuState.value).forEach(k => {
        contextMenuState.value[k] = key
      })
    }
  }

  // getters
  const selectContextMenuKey = (id: string) => {
    return contextMenuState.value[id]
  }

  return {
    // state
    contextMenuState,
    
    // actions
    contextMenuChanged,
    
    // getters
    selectContextMenuKey
  }
})