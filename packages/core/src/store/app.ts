
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // state
  const name = ref('')
  const version = ref('')

  // actions
  const setAppName = (n: string) => {
    name.value = n
  }

  const setAppVersion = (versionString: string) => {
    version.value = versionString
  }

  return {
    // state
    name,
    version,
    
    // actions
    setAppName,
    setAppVersion,
  }
})