import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { PluginCategory, PluginManifest } from '../plugin'

export const usePluginStore = defineStore('plugin', () => {
  // 状态 - 使用 Map 存储插件，id 作为 key
  const pluginsMap = ref(new Map<string, PluginManifest>())
  const pluginsIds = ref<string[]>([])

  // Actions
  const registerAll = (manifests: PluginManifest[]) => {
    const newMap = new Map<string, PluginManifest>()
    const newIds: string[] = []
    
    manifests.forEach(manifest => {
      newMap.set(manifest.id, manifest)
      newIds.push(manifest.id)
    })
    
    pluginsMap.value = newMap
    pluginsIds.value = newIds
  }

  // Getters
  const selectAllPlugins = computed(() => 
    Array.from(pluginsMap.value.values())
  )

  const selectPluginById = (id: string) => 
    computed(() => pluginsMap.value.get(id))

  const selectPluginIds = computed(() => pluginsIds.value)

  const selectPluginTotal = computed(() => pluginsMap.value.size)

  const selectPluginEntities = computed(() => 
    Object.fromEntries(pluginsMap.value)
  )

  const selectPluginsByCategory = (category: PluginCategory) => 
    computed(() => selectAllPlugins.value.filter(plugin => plugin.category === category))

  return {
    pluginsMap,
    pluginsIds,
    registerAll,
    selectAllPlugins,
    selectPluginById,
    selectPluginIds,
    selectPluginTotal,
    selectPluginEntities,
    selectPluginsByCategory,
  }
})