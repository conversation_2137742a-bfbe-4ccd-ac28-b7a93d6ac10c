import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { getSettings, invokeMain, updateSetting } from '../api'
import type { SettingsActualItem, SettingsCheckboxItem, SettingsItem, SettingsRadioItem, SettingsSelectItem, ThemeType } from '../types/types'

export const useSettingsStore = defineStore('settings', () => {
  // State
  const settings = ref<Record<string, SettingsItem>>({})
  const settingsLoaded = ref(false)
  const currentSettingRootId = ref('')

  // Getters (computed)
  const selectAllSettings = computed(() => Object.values(settings.value).sort((a, b) => a.order - b.order))
  const selectSettingIds = computed(() => Object.keys(settings.value))
  const selectSettingsTotal = computed(() => Object.keys(settings.value).length)
  const selectSettingEntities = computed(() => settings.value)

  const selectRootCategoryIds = computed(() => 
    selectAllSettings.value
      .filter(setting => setting.type === 'category' && !setting.parent_id)
      .map(setting => setting.id)
  )

  const selectCurrentSettingRootid = computed(() => currentSettingRootId.value)

  const selectTheme = computed(() => {
    const key = "general.theme"
    if (!settings.value[key]) {
      return localStorage.getItem(key) as ThemeType || "system"
    }
    return ((settings.value[key] as SettingsRadioItem).value || 'system') as ThemeType
  })

  const selectLangCode = computed(() => {
    const key = "general.language"
    let langCode = ""
    if (!settings.value[key]) {
      langCode = localStorage.getItem(key) || "system"
    } else {
      const langSetting = settings.value[key] as SettingsSelectItem
      langCode = langSetting.value || langSetting.default_value
    }

    if (langCode && langCode !== 'system') return langCode
    return navigator.language || navigator.languages[0] || 'zh'
  })

  // Helper functions
  const selectSettingById = (id: string) => settings.value[id]
  
  const selectSubSectionIdsById = (parentId: string) => 
    selectAllSettings.value
      .filter(setting => setting.parent_id === parentId)
      .map(setting => setting.id)

  const selectIsSubCategoryById = (parentId: string) => 
    selectAllSettings.value
      .filter(setting => setting.parent_id === parentId)
      .every(s => s.type === 'category')

  const getSettingValue = <T>(id: string): T | null => {
    const setting = settings.value[id]
    if (!setting) {
      return null
    }

    if (setting.type === 'group' || setting.type === 'category') {
      return null
    }
    
    if (setting.value === null) {
      return setting.default_value as T
    }
    return setting.value as T
  }

  // Actions
  const setCurrentSettingRootId = (id: string) => {
    currentSettingRootId.value = id
  }

  const fetchSettings = async () => {
    if (settingsLoaded.value) return

    let settingsData = await invokeMain<SettingsItem[]>(getSettings())
    settingsData.forEach(s => {
      if (s.type !== 'category' && s.type !== 'group' && s.client_side) {
        s.value = localStorage.getItem(s.id) || s.value
      }
    })

    // Convert array to object for easier access
    const settingsObj: Record<string, SettingsItem> = {}
    settingsData.forEach(item => {
      settingsObj[item.id] = item
    })
    settings.value = settingsObj
    settingsLoaded.value = true

    if (selectSettingIds.value.length) {
      currentSettingRootId.value = selectSettingIds.value.includes('general') ? 'general' : selectSettingIds.value[0]!
    }

    // Special case for omnibar
    const omnibarSetting = settings.value['general.omnibar.enable'] as SettingsCheckboxItem
    const enableOmnibar = (omnibarSetting.value == null || typeof omnibarSetting.value === 'undefined') 
      ? omnibarSetting.default_value 
      : omnibarSetting.value

    if (settings.value['general.omnibar.shortcut']) {
      settings.value['general.omnibar.shortcut'].disabled = !enableOmnibar
    }
  }

  const fetchUpdateSetting = async ({ id, value, client_side, recursive }: {
    id: string,
    value: any,
    client_side?: boolean
    recursive?: boolean
  }) => {
    if (client_side) {
      if (value == null || typeof value === 'undefined') {
        localStorage.removeItem(id)
      } else {
        localStorage.setItem(id, value)
      }
    }

    const result = await invokeMain<{ id: string, value: any }[]>(
      updateSetting({ id, value, recursive: recursive || false })
    )

    if (!result || result.length === 0) {
      return
    }

    result.forEach(change => {
      const setting = settings.value[change.id] as SettingsActualItem

      if (setting) {
        setting.value = change.value
      }
      
      if (change.id === 'general.omnibar.enable') {
        const enabled = getSettingValue<boolean>('general.omnibar.enable')
        if (settings.value['general.omnibar.shortcut']) {
          settings.value['general.omnibar.shortcut'].disabled = !enabled
        }
      }
    })
  }

  return {
    // State
    settings,
    settingsLoaded,
    currentSettingRootId,
    
    // Getters
    selectAllSettings,
    selectSettingIds,
    selectSettingsTotal,
    selectSettingEntities,
    selectRootCategoryIds,
    selectCurrentSettingRootid,
    selectTheme,
    selectLangCode,
    
    // Helper functions
    selectSettingById,
    selectSubSectionIdsById,
    selectIsSubCategoryById,
    getSettingValue,
    
    // Actions
    setCurrentSettingRootId,
    fetchSettings,
    fetchUpdateSetting
  }
})