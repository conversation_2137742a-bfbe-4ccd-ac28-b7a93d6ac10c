import type { MenuItem } from "../types/context-menu";
import { Manager } from "./manager"
import processingTaskItemMenu from "./contextmenus/processing-task-item";

export const RegisteredContextMenu =  {
  'processing-task-item' : "",
  'completed-task-item' : {} as {type: string}
}

export type RegisteredContextMenuType = typeof RegisteredContextMenu

export type ContextMenu<T> = MenuItem<T>[][]

export class ContextMenuManager extends Manager {
  private contextMenus: {
    [K in keyof RegisteredContextMenuType]: ContextMenu<RegisteredContextMenuType[K]>
  } = {
    'completed-task-item': [],
    'processing-task-item': []
  }

  async init() {
    this.contextMenus["processing-task-item"] = processingTaskItemMenu
    this.contextMenus["completed-task-item"] = []
    // TODO: use pinia store
  }

  get(id: keyof RegisteredContextMenuType) {
    return this.contextMenus[id]
  }
}
