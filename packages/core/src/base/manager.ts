import { Emitter } from "./emitter";

const instanceMap = new WeakMap<Function, any>()

export interface IManager<Context, EventMap extends BaseEventMap> extends Emitter<EventMap> {
  initContext(ctx: Context): void
  start(): Promise<void>
  ready(ready?: boolean): void
  waitReady(): Promise<void>
}

export type BaseContext = {}
export type BaseEventMap = {
  'ready': boolean
}
type BaseOptions = {}

export abstract class Manager<C extends BaseContext = {}, E extends BaseEventMap = BaseEventMap>
  extends Emitter<E> implements IManager<C, E> {
  protected ctx!: C
  public isReady = false

  // init
  initContext(ctx: C) {
    this.ctx = ctx
  }

  // start
  async start() {
  }

  ready(ready = true) {
    this.isReady = ready
    this.fire('ready', ready)
  }

  async waitReady() {
    if (this.isReady) return
    return new Promise<void>(resolve => {
      this.once('ready', ready => {
        if (ready) resolve()
      })
    })
  }

}


export type ManagerCtor<T> = {
  new(): T
}

export type ManagerCtorWithOption<T, O = {}> = {
  new(o: O): T
}

export function getManager<T, O = {}>(M: ManagerCtorWithOption<T, O>, option: O): T
export function getManager<T, O = {}>(M: ManagerCtorWithOption<T, O>): T
export function getManager<T, O = {}>(M: ManagerCtor<T>): T
export function getManager<T, O = {}>(M: ManagerCtor<T> | ManagerCtorWithOption<T, O>, option?: O): T {
  const existing = instanceMap.get(M)
  if (existing) return existing

  const instance = new M(option as any)
  instanceMap.set(M, instance)
  return instance
}

export function hasManager<T, O = {}>(M: ManagerCtorWithOption<T, O>): boolean
export function hasManager<T>(M: ManagerCtor<T>): boolean
export function hasManager<T, O = {}>(M: ManagerCtor<T> | ManagerCtorWithOption<T, O>): boolean {
// export function hasManager<T>(M: ManagerCtor<T>): boolean {
  return instanceMap.has(M)
}
