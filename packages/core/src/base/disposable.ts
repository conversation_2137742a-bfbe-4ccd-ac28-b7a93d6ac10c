

if (!(globalThis as any)['garbage']) {
    (globalThis as any)['garbage'] = new WeakMap()
}

export interface IDisposable {
    dispose: () => void
}

export abstract class Disposable implements IDisposable {
    private _toDispose = new Set<IDisposable>()
    constructor() {
    }

    dispose() {
        (globalThis as any)['garbage'].set(this, this.identifier)

        for (let d of this._toDispose.values()) {
            d.dispose()
        }
        this._toDispose.clear()
    }

    protected _register(t: IDisposable): void {
        if ((t as unknown as Disposable) == this) {
            throw new Error('Cannot register a disposable on itself')
        }
        this._toDispose.add(t)
    }

    get identifier() {
        return ''
    }
}
