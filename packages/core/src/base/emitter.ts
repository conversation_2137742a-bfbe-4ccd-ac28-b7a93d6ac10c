import { Disposable } from "./disposable";
import type { IDisposable } from "./disposable";
// import { remove } from "lodash";


interface IListener<K extends keyof T, T> extends IDisposable {
  once?: boolean
  func: (args: T[K]) => unknown
}

// key => arg
type EventMap = {
  [k: string]: any
}

// {
//   a: number,
//   b: string
// }

type ListenerMap<T> = Map<keyof T, IListener<keyof T, T>[]>

// export interface IEmitter<T extends EventMap> {
//   fire<K extends keyof T>(eventName: K, args?: T[K]): void
//   on<K extends keyof T>(eventName: K, func: (args?: T[K]) => unknown): IListener<keyof T, T>
// }

function remove<T>(arr: T[], el: T) {
  const elIndex = arr.findIndex(e => e === el)
  if (elIndex >= 0) {
    arr.splice(elIndex, 1)
  }
}

export class Emitter<T extends EventMap> extends Disposable {
  private _eventListeners: ListenerMap<T> = new Map()

  constructor() {
    super()
  }

  fire<K extends keyof T>(eventName: K, args?: T[K]): void;
  fire<K extends keyof T>(eventName: K, args: T[K]) {
    let listeners = this._eventListeners.get(eventName)
    if (!listeners) return

    listeners.slice().forEach(listener => {
      listener.func(args)
      if (listener.once) {
        remove(listeners!, listener)
      }
    })
  }

  on<K extends keyof T>(eventName: K, func: (args: T[K]) => unknown): IListener<keyof T, T> {
    let listener: any/* IListener<keyof T, T> */ = {
      func,
      dispose: () => {
        let listeners = this._eventListeners.get(eventName)
        if (listeners) {
          remove(listeners, listener)
        }
      }
    }

    if (this._eventListeners.has(eventName)) {
      this._eventListeners.get(eventName)!.push(listener)
    } else {
      this._eventListeners.set(eventName, [listener])
    }
    return listener
  }

  once<K extends keyof T>(eventName: K, func: (args: T[K]) => unknown): IListener<keyof T, T> {
    let listener: any/* IListener<keyof T, T> */ = {
      func,
      once: true,
      dispose: () => {
        let listeners = this._eventListeners.get(eventName)
        if (listeners) {
          remove(listeners, listener)
        }
      }
    }

    if (this._eventListeners.has(eventName)) {
      this._eventListeners.get(eventName)!.push(listener)
    } else {
      this._eventListeners.set(eventName, [listener])
    }
    return listener
  }

  override dispose() {
    super.dispose()
    this._eventListeners.clear()
  }
}