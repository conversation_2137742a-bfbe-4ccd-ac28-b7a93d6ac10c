import { KeyBinding } from "src/types/key-binding";
import type { ContextMenu, RegisteredContextMenuType } from "../contextmenu";

const menus: ContextMenu<RegisteredContextMenuType['processing-task-item']> = [
  [
    {
      id: "retry",
      name: "Re<PERSON>",
      icon: "xx"
    },
    {
      id: "cancel",
      name: "Cancel",
      shortcut: KeyBinding.fromStr('Command+W')
    },
  ]
]




export default menus;