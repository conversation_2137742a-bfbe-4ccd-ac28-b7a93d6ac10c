import { useAppStore } from 'src/store/app'
import { useSettingsStore } from '../store/settings'

export function getTranslation(scope?: string) {
  return (key: string, placeholders?: Record<string, any>): string => {
    const settings = useSettingsStore()
    const app = useAppStore()
    const langCode = settings.selectLangCode
    scope = scope || app.name

    let scopedKey = key
    if (scope) {
      scopedKey = `${scope}.${key}`
    }

    const lng = window.__LANG_MAP__[langCode] || window.__LANG_MAP__.zh!
    let translateStr = lng[scopedKey] || lng[key] || key
    
    if (placeholders) {
      Object.entries(placeholders).forEach(([k, v]) => {
        translateStr = translateStr.replace(`{{${k}}}`, v)
      })
    }
    
    return translateStr
  }
}
