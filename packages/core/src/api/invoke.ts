import { invoke } from "@tauri-apps/api/core";


type InvokeArg<T extends object = any> = {
  plugin_id?: string,
  method: string,
  payload?: T
}

type InvokeFunc<T extends object> = (payload?: T) => InvokeArg<T>;

export function $invoker<T extends object = any>(method: string): InvokeFunc<T>;
export function $invoker<T extends object = any>(plugin_id: string, method: string): InvokeFunc<T>;
export function $invoker<T extends object = any>(arg1: string, arg2?: string): InvokeFunc<T> {
  return (payload?: T): InvokeArg<T> => ({
    plugin_id: typeof arg2 === 'undefined' ? undefined : arg1,
    method: typeof arg2 === 'undefined' ? arg1 : arg2,
    payload
  });
}

export function invokeMain<T>(arg: InvokeArg) {
  return invoke<T>(arg.method, arg.payload)
}

export function invokePlugin<T>(arg: InvokeArg) {
  return invoke<T>("call_plugin", {
    meta: {
      id: arg.plugin_id,
      method: arg.method,
      payload: arg.payload
    }
  })
}

