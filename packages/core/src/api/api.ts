import type { KeyBindingType } from 'src/types/key-binding';
import { $invoker } from './invoke'

export const greetMain = $invoker<{ name: string }>("greet");
export const getLang = $invoker<{ code: string }>("get_lang");
export const getLangMap = $invoker("get_lang_map");
export const getSettings = $invoker("get_settings");
export const updateSetting = $invoker<{
  id: string;
  value: string;
  recursive: boolean;
}>("update_setting");

export const registerGlobalShortcut = $invoker<{ keyBinding: KeyBindingType }>("register_global_shortcut")
export const unregisterGlobalShortcut = $invoker<{ keyBinding: KeyBindingType }>("unregister_global_shortcut")

const plugin_id = "example_plugin";
export const greetPlugin = $invoker<{ name: string }>(plugin_id, "greet");

