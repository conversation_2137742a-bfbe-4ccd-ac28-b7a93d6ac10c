import { getCurrentWindow } from "@tauri-apps/api/window";

export async function setupAppWindow(): Promise<void> {
  const appWindow = getCurrentWindow();

  // Restore window state before showing the window
  // await restoreStateCurrent();

  // Show the window after restoring state
  appWindow.show();

  // Set up window close event listener to save state
  // const unlisten = await appWindow.onCloseRequested(async () => {
  //   // Save window state before closing
  //   // await saveWindowState();
  // });

  // Clean up listener on component unmount
  // return () => {
  //   unlisten();
  // };
}