
import { emit, listen } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';
import { PluginManager, type PluginManifest } from '../plugin';
import { getManager } from '../base/manager';
import { usePluginStore } from 'src/store/plugin';

// emit(eventName, payload)
// emit('file-selected', '/path/to/file');

// const appWebview = getCurrentWebviewWindow();
// appWebview.emit('route-changed', { url: window.location.href });

export async function ready() {
  listen<PluginManifest[]>('plugin:installed', async (e) => {
    const plugin = usePluginStore()
    plugin.registerAll(e.payload)
    
    const pluginManager = getManager(PluginManager)
    const plugins = await pluginManager.registerAll(e.payload)
  })

  const appWebview = getCurrentWebviewWindow()
  await appWebview.emit('ready')
}