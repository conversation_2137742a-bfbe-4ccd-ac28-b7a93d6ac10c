import { debug, error, info, trace, warn } from '@tauri-apps/plugin-log';


export function forwardConsole(
  fnName: 'log' | 'debug' | 'info' | 'warn' | 'error',
  logger: (message: string) => Promise<void>
) {
  const original = console[fnName];
  console[fnName] = (message, ...args) => {
    // original(message, ...args);
    // merge message and args(which can be any type)
    let result = message;
    for (const arg of args) {
      if (typeof arg === 'object') {
        result += JSON.stringify(arg);
      } else {
        result += `${arg}`;
      }
    }

    logger(result).catch(e => {
      console.error('log error', e)
    });
  };
}

export function redirectLogs() {
  forwardConsole('log', trace);
  forwardConsole('debug', debug);
  forwardConsole('info', info);
  forwardConsole('warn', warn);
  forwardConsole('error', error);
}
