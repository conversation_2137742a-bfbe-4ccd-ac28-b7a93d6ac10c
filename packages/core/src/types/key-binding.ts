import { platform } from "../tauri/index";

export class KeyBinding {
  private showModifiers: string[] = [];
  private modifiers: string[] = [];
  private actualKey: string = ''
  constructor(
    public altKey: boolean,
    public ctrlKey: boolean,
    public shiftKey: boolean,
    public metaKey: boolean,
    public key: string,
    public code: string
  ) {
    if (key !== 'Control' && key !== 'Shift' && key !== 'Alt' && key !== 'Meta' && key !== 'Dead' && key !== 'Unidentified') {
      this.actualKey = key
    } else {
      this.actualKey = ''
    }

    if (metaKey) {
      this.showModifiers.push('Meta')
      this.modifiers.push('Super')
    }
    if (ctrlKey) {
      this.showModifiers.push('Ctrl')
      this.modifiers.push('Ctrl')
    }
    if (altKey) {
      this.showModifiers.push('Alt')
      this.modifiers.push('Alt')
    }
    if (shiftKey) {
      this.showModifiers.push('Shift')
      this.modifiers.push('Shift')
    }

  }

  static from(type: KeyBindingType) {
    return new KeyBinding(
      type.altKey,
      type.ctrlKey,
      type.shiftKey,
      type.metaKey,
      type.key,
      type.code
    )
  }

  static fromStr(key: string) {
    const keys = key.split('+').map(k => k.toLowerCase())
    const altKey = keys.includes('alt') || keys.includes('option')
    const ctrlKey = keys.includes('ctrl') || keys.includes('control')
    const shiftKey = keys.includes('shift')
    const metaKey = keys.includes('meta') || keys.includes('command') || keys.includes('win') || keys.includes('super') || keys.includes('cmd')
    const actualKey = keys.pop()!

    return new KeyBinding(
      altKey,
      ctrlKey,
      shiftKey,
      metaKey,
      actualKey,
      // TODO
      actualKey,
    )
  }

  get formattedKey() {
    if (this.actualKey.length === 1) {
      return this.actualKey.toUpperCase()
    }
    return this.actualKey
  }

  display() {
    const p = platform()
    if (p === 'macos') {
      let str = ''
      if (this.metaKey) {
        str += MacOSEmojiMap.command
      }
      if (this.ctrlKey) {
        str += MacOSEmojiMap.control
      }
      if (this.altKey) {
        str += MacOSEmojiMap.option
      }
      if (this.shiftKey) {
        str += MacOSEmojiMap.shift
      }
      str += this.key.toUpperCase()
      return str
    }

    return this.show()
  }

  toString() {
    if (!this.modifiers.length) {
      return this.code
    }

    return this.modifiers.join('+') + '+' + this.code
  }

  show() {
    if (!this.showModifiers.length) {
      return this.formattedKey
    }

    return this.showModifiers.join('+') + '+' + this.formattedKey
  }

  get isValid() {
    return !!this.actualKey
  }

  toType(): KeyBindingType {
    return {
      altKey: this.altKey,
      ctrlKey: this.ctrlKey,
      metaKey: this.metaKey,
      shiftKey: this.shiftKey,
      key: this.key,
      code: this.code
    }
  }


}

export type KeyBindingType = {
  altKey: boolean,
  ctrlKey: boolean,
  shiftKey: boolean,
  metaKey: boolean,
  key: string,
  code: string
}

function keyToCode(key: string) {

}


export const MacOSEmojiMap = {
  "command": "⌘",
  "option": "⌥",
  "shift": "⇧",
  "control": "⌃",
  "enter": "⏎",
  "backspace": "⌫",
  "tab": "⇥",
  "space": "␣",
  "up": "↑",
  "down": "↓",
  "left": "←",
  "right": "→",
  "esc": "esc",
  "delete": "⌦",
  "pageup": "⇞",
  "pagedown": "⇟",
  "home": "↖",
  "end": "↘",
  "capslock": "⇪",
  "numlock": "⇭",
  "printscreen": "Print",
  "scrolllock": "Scroll",
  "pause": "Pause",
  "insert": "Ins",
}