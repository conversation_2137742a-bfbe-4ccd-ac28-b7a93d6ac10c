import type { KeyBinding } from "./key-binding"

// a function
type FrontendMenuItemCall<T> = (arg: T) => void
// a tauri command
type BackendMenuItemCall<T> = { method: string, payload: T }
// a path to js: (for import)
type CustomMenuItemCall = string

// T is call type
export type MenuItem<T> = {
  id: string
  name: string
  icon?: string
  shortcut?: KeyBinding
  confirm?: string
  callback?: FrontendMenuItemCall<T> | BackendMenuItemCall<T> | CustomMenuItemCall
  danger?: boolean
  disabled?: boolean
  children?: MenuItem<T>[]
}