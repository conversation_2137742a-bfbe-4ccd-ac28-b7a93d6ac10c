import type { KeyBindingType } from "./key-binding"

export type SettingsType = 'category' | 'section' | 'item'
export type SettingsItemType = 'input' | 'select' | 'checkbox' | 'radio'

export type SettingsItem = 
  SettingsCategory |
  SettingsGroup |
  SettingsActualItem


type BaseSettings = {
  id: string
  name: string
  parent_id: string
  description?: string
  order: number
  disabled?: boolean
  client_side?: boolean
}

export type SettingsCategory = BaseSettings & {
  type: 'category'
}
export type SettingsGroup = BaseSettings & {
  type: 'group'
}

type BaseActualSettings<T> = BaseSettings & {
  value: T | null,
  default_value: T,
}

export type SettingsActualItem = 
  SettingsInputItem | 
  SettingsCheckboxItem |
  SettingsKeyboardItem |
  SettingsSelectItem |
  SettingsRadioItem

export type SettingsInputItem = BaseActualSettings<string> & {
  type: 'input',
}

export type SettingsCheckboxItem = BaseActualSettings<boolean> & {
  type: 'checkbox'
}

export type SettingsKeyboardItem = BaseActualSettings<KeyBindingType> & {
  type: 'keyboard'
}

export type SettingsSelectItem = BaseActualSettings<string> & {
  type: 'select'
  options: SelectOption[]
}

export type SettingsRadioItem = BaseActualSettings<string> & {
  type: 'radio'
  options: SelectOption[]
}

export type SelectOption = { name: string, value: string }
export type ThemeType = "dark" | "light" | "system"
