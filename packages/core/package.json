{"name": "@siren/core", "version": "1.0.0", "module": "./dist/index.js", "devDependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-os": "~2", "@tauri-apps/plugin-log": "~2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-window-state": "^2.4.0"}, "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./tauri": {"import": "./dist/tauri/index.js", "types": "./dist/tauri/index.d.ts"}, "./utils": {"import": "./dist/utils/index.js", "types": "./dist/utils/index.d.ts"}, "./api": {"import": "./dist/api/index.js", "types": "./dist/api/index.d.ts"}, "./plugin": {"import": "./dist/plugin/index.js", "types": "./dist/plugin/index.d.ts"}, "./render": {"import": "./dist/render/index.js", "types": "./dist/render/index.d.ts"}}, "files": ["dist"], "license": "UNLICENSED", "scripts": {"dev": "vite", "watch": "tsdown --watch", "check": "tsc", "build": "tsdown", "preview": "vite preview"}, "type": "module", "types": "./dist/index.d.ts"}