{"name": "siren", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"pinia": "^3.0.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/bun": "latest", "@vitejs/plugin-vue": "^5.2.1", "rollup-plugin-external-globals": "^0.13.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-external-globals": "workspace:*", "vite-plugin-importmap": "workspace:*", "vue-tsc": "^2.1.10"}}